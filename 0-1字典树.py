import os
import sys
input = sys.stdin.readline
print = sys.stdout.write
class TreeNode():
    def __init__(self):
        self.node = {}
        self.is_num = 0
    def insert(self,s):
        curr = self
        for i in range(31,-1,-1):
            c = (s>>i)&1
            if c not in curr.node.keys():
                curr.node[c]=TreeNode()
            curr = curr.node[c]
        curr.is_num = s
    def query(self,s):
        curr = self
        for i in range(31,-1,-1):
            c = (s>>i)&1
            if c^1 in curr.node.keys():
                curr = curr.node[c^1]
            else:
                curr = curr.node[c]
        return curr.is_num
n = int(input())
a = list(map(int,input().split()))
root = TreeNode()
for c in a:
    root.insert(c)
q = int(input())
for i in range(q):
    num = int(input())
    print(str(root.query(num)^num)+'\n')
