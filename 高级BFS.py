import sys
from queue import PriorityQueue
input = sys.stdin.readline
n,m=map(int,input().split())
a = []
for i in range(n):
  a.append(list(map(int,input().split())))
q = PriorityQueue()
inf = 1e9
#vis[i][j]表示判断i,j是否出队列
vis = [[False]*m for i in range(n)]
#dis[i][j]表示位置i，j被染色完成的时间
dis = [[inf]*m for i in range(n)]
dis[0][0] = a[0][0]
q.put((dis[0][0],0,0))
ans = 0
while not q.empty():
  d,x,y = q.get()
  if vis[x][y]:
    continue
  vis[x][y] = True
  ans = max(ans,d)#确认出队之后更新答案
  for dx,dy in [(1,0),(-1,0),(0,1),(0,-1)]:
    nx,ny = x+dx,y+dy
    if 0<=nx<n and 0<=ny<m and not vis[nx][ny]:#如果没有出过队列，那么一定有一个最小值等着出队列
      dis[nx][ny] = min(dis[nx][ny],d+a[nx][ny])
      q.put((dis[nx][ny],nx,ny))
print(ans)
