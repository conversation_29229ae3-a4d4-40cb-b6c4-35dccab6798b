# Python算法模板学习总结

> 本文档整理了20个Python算法模板，涵盖动态规划、树与图算法、数据结构、字符串算法等核心知识点。
> 
> **创建日期**: 2025-08-01  
> **文件数量**: 20个算法模板  
> **适用场景**: 蓝桥杯竞赛、算法学习

## 目录
1. [动态规划算法](#1-动态规划算法)
2. [树与图算法](#2-树与图算法)  
3. [数据结构](#3-数据结构)
4. [字符串算法](#4-字符串算法)
5. [数学与工具算法](#5-数学与工具算法)

---

## 1. 动态规划算法

### 1.1 0-1背包问题

**算法描述**: 给定n个物品和容量为V的背包，每个物品只能选择一次，求最大价值。

**核心思想**: dp[i][j]表示前i个物品在容量j下的最大价值，状态转移方程为：
- 不选第i个物品：dp[i][j] = dp[i-1][j]
- 选第i个物品：dp[i][j] = dp[i-1][j-w[i]] + v[i]

**代码实现**:
```python
# 方法1：二维DP（最本质）
n,V = map(int,input().split())  # n个物品，背包容量为V
dp = [[0]*(V+1) for _ in range(n+1)]  # dp[i][j]表示前i个物品，背包容量为j时的最大价值
for i in range(1,n+1):
    w,v = map(int,input().split())
    for j in range(1,V+1):
        if j>=w:
            dp[i][j] = max(dp[i-1][j], dp[i-1][j-w]+v)
        else:
            dp[i][j] = dp[i-1][j]
print(dp[n][V])

# 方法2：一维DP优化
n,V = map(int,input().split())
dp = [0]*(V+1)
for i in range(1,n+1):
    w,v = map(int,input().split())
    for j in range(V,w-1,-1):  # 逆序遍历避免重复使用
        dp[j] = max(dp[j], dp[j-w]+v)
print(dp[V])
```

**复杂度分析**: 时间复杂度O(n*V)，空间复杂度O(V)

**应用场景**: 资源分配、选择问题

### 1.2 完全背包问题

**算法描述**: 每个物品可以选择无限次，求最大价值。

**核心思想**: 与0-1背包的区别在于状态转移时可以重复选择当前物品。

**代码实现**:
```python
# 方法1：二维DP
N,V = map(int,input().split())
dp = [[0]*(V+1) for i in range(N+1)]
for i in range(1,N+1):
    wi,vi = map(int,input().split())
    for j in range(1,V+1):
        if j<wi:
            dp[i][j] = dp[i-1][j]
        else:
            dp[i][j] = max(dp[i-1][j], dp[i][j-wi]+vi)  # 注意这里是dp[i][j-wi]
print(dp[N][V])

# 方法2：一维DP优化
N,V = map(int,input().split())
dp = [0]*(V+1)
for i in range(1,N+1):
    wi,vi = map(int,input().split())
    for j in range(wi,V+1):  # 正序遍历
        dp[j] = max(dp[j], dp[j-wi]+vi)
print(dp[V])
```

**复杂度分析**: 时间复杂度O(n*V)，空间复杂度O(V)

### 1.3 多重背包问题

**算法描述**: 每个物品有数量限制si，求最大价值。

**核心思想**: 通过二进制优化将多重背包转化为0-1背包问题。

**代码实现**:
```python
# 二进制优化版本
N,V = map(int,input().split())
w_v = []  # 存储转换后的物品
for i in range(N):
    wi,vi,si = map(int,input().split())
    k = 1
    while si>=k:
        w_v.append((k*wi, k*vi))
        si -= k
        k *= 2
    if si != 0:
        w_v.append((si*wi, si*vi))

# 转换为0-1背包
dp = [0]*(V+1)
for w,v in w_v:
    for j in range(V,w-1,-1):
        dp[j] = max(dp[j], dp[j-w]+v)
print(dp[V])
```

**复杂度分析**: 时间复杂度O(V*Σlog(si))，空间复杂度O(V)

### 1.4 最长上升子序列和最长下降子序列

**算法描述**: 求数组中最长上升子序列和最长下降子序列的长度。

**代码实现**:
```python
n = int(input())
a = [0]+list(map(int,input().split()))
dp = [0]*(n+1)  # dp[i]表示以a[i]结尾的最长上升子序列长度
for i in range(1,n+1):
    dp[i] = 1  # 初始化
    for j in range(1,i):
        if a[j] < a[i]:
            dp[i] = max(dp[i], dp[j]+1)

dp1 = [0]*(n+1)  # dp1[i]表示以a[i]开头的最长下降子序列长度
for i in range(n,0,-1):
    dp1[i] = 1  # 初始化
    for j in range(i+1,n+1):
        if a[j] < a[i]:
            dp1[i] = max(dp1[i], dp1[j]+1)

ans = max([dp[i]+dp1[i]-1 for i in range(1,n+1)])
print(n-ans)
```

**复杂度分析**: 时间复杂度O(n²)，空间复杂度O(n)

### 1.5 最长公共子序列

**算法描述**: 求两个序列的最长公共子序列长度。

**代码实现**:
```python
n,m = map(int,input().split())
a = [0]+list(map(int,input().split()))
b = [0]+list(map(int,input().split()))
dp = [[0]*(m+1) for i in range(n+1)]
# dp[i][j]表示a的前i个元素和b的前j个元素的最长公共子序列长度
for i in range(1,n+1):
    for j in range(1,m+1):
        if a[i] == b[j]:
            dp[i][j] = dp[i-1][j-1] + 1
        else:
            dp[i][j] = max(dp[i-1][j], dp[i][j-1])
print(dp[n][m])
```

**复杂度分析**: 时间复杂度O(n*m)，空间复杂度O(n*m)

### 1.6 最长回文字符串

**算法描述**: 使用Manacher算法求最长回文子串长度。

**代码实现**:
```python
def extend(s,l,r):
    while l>=0 and r<len(s) and s[l]==s[r]:
        l -= 1
        r += 1
    return (r-l-2)//2

def longestPalindroma(s):
    s = '#'+'#'.join(list(s))+'#'  # 预处理字符串
    dp = [0]*(len(s))
    center, right = 0, 0
    for i in range(1,len(s)):
        if i > right:
            dp[i] = extend(s,i,i)
        else:
            i_sym = center*2-i
            min_len = min(dp[i_sym], right-i)
            dp[i] = extend(s, i-min_len, i+min_len)
        if i+dp[i] > right:
            center, right = i, i+dp[i]
    return max(dp)

s = input()
print(longestPalindroma(s))
```

**复杂度分析**: 时间复杂度O(n)，空间复杂度O(n)

---

## 2. 树与图算法

### 2.1 LCA (最近公共祖先)

**算法描述**: 使用倍增算法求树上两点的最近公共祖先。

**核心思想**: 预处理每个节点向上走2^i步的祖先，查询时先调整深度再二分查找。

**代码实现**:
```python
import sys
sys.setrecursionlimit(10000)
input = sys.stdin.readline

def dfs(u,fa):
    deep[u] = deep[fa]+1
    p[u][0] = fa
    for i in range(1,21):
        p[u][i] = p[p[u][i-1]][i-1]
    for v in G[u]:
        if v == fa:
            continue
        dfs(v,u)

def lca(x,y):
    if deep[x] < deep[y]:
        x,y = y,x  # 保证x更深
    # 调整深度
    for i in range(20,-1,-1):
        if deep[p[x][i]] >= deep[y]:
            x = p[x][i]
    if x == y:
        return x
    # 二分查找LCA
    for i in range(20,-1,-1):
        if p[x][i] != p[y][i]:
            x,y = p[x][i], p[y][i]
    return p[x][0]

# 输入处理
n = int(input())
G = [[] for i in range(n+1)]
for _ in range(n-1):
    u,r = map(int,input().split())
    G[u].append(r)
    G[r].append(u)

# 预处理
deep = [0]*(n+1)
p = [[0]*21 for i in range(n+1)]
dfs(1,0)

# 查询
q = int(input())
for _ in range(q):
    a,b = map(int,input().split())
    print(lca(a,b))
```

**复杂度分析**: 预处理O(n log n)，查询O(log n)

### 2.2 树上差分

**算法描述**: 在树上进行路径加权操作，最后统一计算每个节点的权值。

**代码实现**:
```python
# 树上差分模板（结合LCA）
import sys
sys.setrecursionlimit(100000)
input = sys.stdin.readline

n,k = map(int,input().split())
G = [[] for _ in range(n+1)]
for i in range(n-1):
    u,v = map(int,input().split())
    G[u].append(v)
    G[v].append(u)

# LCA预处理（代码同上）
deep = [0]*(n+1)
p = [[0]*21 for i in range(n+1)]

def dfs(u,fa):
    deep[u] = deep[fa]+1
    p[u][0] = fa
    for i in range(1,21):
        p[u][i] = p[p[u][i-1]][i-1]
    for v in G[u]:
        if v == fa:
            continue
        dfs(v,u)

def lca(x,y):
    if deep[x] < deep[y]:
        x,y = y,x
    for i in range(20,-1,-1):
        if deep[p[x][i]] >= deep[y]:
            x = p[x][i]
    if x == y:
        return x
    for i in range(20,-1,-1):
        if p[x][i] != p[y][i]:
            x,y = p[x][i], p[y][i]
    return p[x][0]

dfs(1,0)

# 树上差分
c = [0]*(n+1)
for i in range(k):
    x,y = map(int,input().split())
    c[x] += 1
    c[y] += 1
    c[lca(x,y)] -= 1
    c[p[lca(x,y)][0]] -= 1

def dfs2(u,fa):
    for v in G[u]:
        if v == fa:
            continue
        dfs2(v,u)
        c[u] += c[v]

dfs2(1,0)
print(max(c))
```

**复杂度分析**: 时间复杂度O(n log n + k log n)，空间复杂度O(n log n)

### 2.3 树的直径

**算法描述**: 使用两次DFS求树的直径（最长路径）。

**核心思想**: 第一次DFS找到距离起点最远的点，第二次DFS从该点出发找最远距离。

**代码实现**:
```python
n = int(input())
G = [[] for _ in range(n+1)]
for _ in range(n-1):
    u,v,w = map(int,input().split())
    G[u].append([v,w])
    G[v].append([u,w])

def dfs(u,fa,d):
    global s
    if d[u] > d[s]:
        s = u
    for v,w in G[u]:
        if v == fa:
            continue
        d[v] = d[u] + w
        dfs(v,u,d)

d = [0]*(n+1)
s = 1
dfs(1,0,d)  # 第一次DFS
d[s] = 0
d1 = [0]*(n+1)
dfs(s,0,d1)  # 第二次DFS
ans = d1[s]
print(ans)
```

**复杂度分析**: 时间复杂度O(n)，空间复杂度O(n)

### 2.4 多源点BFS

**算法描述**: 从多个起点同时开始BFS，求所有点到最近起点的距离。

**代码实现**:
```python
from collections import deque
import sys
input = sys.stdin.readline

q = deque()
n,m,t = map(int,input().split())
dis = [[-1]*(m+1) for i in range(n+1)]  # dis[i][j]表示暖气到达(i,j)的时间
ans = 0

# 多源点初始化
for i in range(t):
    x,y = map(int,input().split())
    q.append((x,y))
    dis[x][y] = 0

# BFS
while q:
    x,y = q.popleft()
    ans = max(ans, dis[x][y])
    for dx,dy in [(-1,0),(1,0),(0,-1),(0,1),(1,1),(-1,-1),(1,-1),(-1,1)]:
        nx,ny = x+dx, y+dy
        if 1<=nx<=n and 1<=ny<=m and dis[nx][ny]==-1:
            dis[nx][ny] = dis[x][y] + 1
            q.append((nx,ny))

print(ans)
```

**复杂度分析**: 时间复杂度O(n*m)，空间复杂度O(n*m)

### 2.5 高级BFS (Dijkstra变种)

**算法描述**: 使用优先队列的BFS，适用于带权图的最短路径问题。

**代码实现**:
```python
import sys
from queue import PriorityQueue
input = sys.stdin.readline

n,m = map(int,input().split())
a = []
for i in range(n):
    a.append(list(map(int,input().split())))

q = PriorityQueue()
inf = 1e9
vis = [[False]*m for i in range(n)]  # 是否出队列
dis = [[inf]*m for i in range(n)]   # 到达时间

dis[0][0] = a[0][0]
q.put((dis[0][0], 0, 0))
ans = 0

while not q.empty():
    d,x,y = q.get()
    if vis[x][y]:
        continue
    vis[x][y] = True
    ans = max(ans, d)
    
    for dx,dy in [(1,0),(-1,0),(0,1),(0,-1)]:
        nx,ny = x+dx, y+dy
        if 0<=nx<n and 0<=ny<m and not vis[nx][ny]:
            dis[nx][ny] = min(dis[nx][ny], d+a[nx][ny])
            q.put((dis[nx][ny], nx, ny))

print(ans)
```

**复杂度分析**: 时间复杂度O(nm log(nm))，空间复杂度O(nm)

---

## 3. 数据结构

### 3.1 字典树 (Trie)

**算法描述**: 用于高效存储和查询字符串集合的树形数据结构。

**代码实现**:
```python
class TreeNode():
    def __init__(self):
        self.nodes = {}
        self.cnt = 0
    
    def insert(self, s):
        curr = self
        for i in s:
            if i not in curr.nodes.keys():
                curr.nodes[i] = TreeNode()
            curr = curr.nodes[i]
            curr.cnt += 1
    
    def check(self, s):
        curr = self
        for i in s:
            if i not in curr.nodes.keys():
                return 0
            curr = curr.nodes[i]
        return curr.cnt

n,m = map(int,input().split())
root = TreeNode()
for i in range(n):
    s = input()
    root.insert(s)
for i in range(m):
    s = input()
    print(root.check(s))
```

**复杂度分析**: 插入和查询均为O(L)，L为字符串长度

### 3.2 0-1字典树

**算法描述**: 专门处理二进制数的字典树，常用于异或相关问题。

**代码实现**:
```python
import os
import sys
input = sys.stdin.readline
print = sys.stdout.write

class TreeNode():
    def __init__(self):
        self.node = {}
        self.is_num = 0
    
    def insert(self, s):
        curr = self
        for i in range(31, -1, -1):  # 从高位到低位
            c = (s>>i) & 1
            if c not in curr.node.keys():
                curr.node[c] = TreeNode()
            curr = curr.node[c]
        curr.is_num = s
    
    def query(self, s):
        curr = self
        for i in range(31, -1, -1):
            c = (s>>i) & 1
            if c^1 in curr.node.keys():  # 优先选择不同的位
                curr = curr.node[c^1]
            else:
                curr = curr.node[c]
        return curr.is_num

n = int(input())
a = list(map(int,input().split()))
root = TreeNode()
for c in a:
    root.insert(c)

q = int(input())
for i in range(q):
    num = int(input())
    print(str(root.query(num)^num)+'\n')
```

**复杂度分析**: 时间复杂度O(32)，空间复杂度O(32*n)

### 3.3 并查集

**算法描述**: 用于处理集合合并和查询问题的数据结构。

**代码实现**:
```python
class UnionFind:
    def __init__(self, n):
        self.fa = [i for i in range(n)]
        self.size = [1] * n
    
    def find(self, x):
        if x == self.fa[x]:
            return x
        self.fa[x] = self.find(self.fa[x])  # 路径压缩
        return self.fa[x]
    
    def union(self, x, y):
        rx, ry = self.find(x), self.find(y)
        if rx == ry:
            return
        self.fa[ry] = rx
        self.size[rx] += self.size[ry]

def cal(x):
    return x*(x-1)//2

n,m,l,r = map(int,input().split())
uf1 = UnionFind(n+1)
uf2 = UnionFind(n+1)

for i in range(m):
    u,v,w = map(int,input().split())
    if w <= r:
        uf1.union(u,v)
    if w < l:
        uf2.union(u,v)

ans = 0
for i in range(1,n+1):
    if i == uf1.find(i):
        ans += cal(uf1.size[i])
    if i == uf2.find(i):
        ans -= cal(uf2.size[i])
print(ans)
```

**复杂度分析**: 时间复杂度近似O(1)，空间复杂度O(n)

### 3.4 单调栈

**算法描述**: 维护栈内元素单调性的数据结构，用于求解下一个更大/更小元素。

**代码实现**:
```python
def right_bigger(a, n):
    ans = [-1]*(n+1)
    stack = []
    for i, x in enumerate(a):
        if i == 0:
            continue
        while stack and a[stack[-1]] < x:
            ans[stack[-1]] = i
            stack.pop()
        stack.append(i)
    return ans[1:]

def left_bigger(a, n):
    ans = [-1]*(n+1)
    stack = []
    for i in range(n, 0, -1):
        x = a[i]
        while stack and a[stack[-1]] < x:
            ans[stack[-1]] = i
            stack.pop()
        stack.append(i)
    return ans[1:]

N = int(input())
lst = [0] + list(map(int,input().split()))
print(*left_bigger(lst, N))
print(*right_bigger(lst, N))
```

**复杂度分析**: 时间复杂度O(n)，空间复杂度O(n)

---

## 4. 字符串算法

### 4.1 KMP算法

**算法描述**: 高效的字符串模式匹配算法，避免不必要的回溯。

**核心思想**: 利用已匹配的信息，通过Next数组跳过不可能匹配的位置。

**代码实现**:
```python
n = int(input())
s = input()
Next = [0]*(n+1)  # Next[i]表示前i个元素的最长公共前后缀长度

def get_next(t):
    for i in range(1, len(t)):
        j = Next[i]
        while j > 0 and t[i] != t[j]:
            j = Next[j]
        if t[i] == t[j]:
            Next[i+1] = j + 1
        else:
            Next[i+1] = 0

def KMP(s, t):
    get_next(t)
    j = 0
    ans = 0
    for i in range(len(s)):
        while j > 0 and t[j] != s[i]:
            j = Next[j]
        if t[j] == s[i]:
            j += 1
        if j == len(t):
            ans += 1
            j = Next[j]
    return ans

count = 0
for i in range(1, n+1):
    t = s[:i]
    count += KMP(s, t)
print(count)
```

**复杂度分析**: 时间复杂度O(n+m)，空间复杂度O(m)

---

## 5. 数学与工具算法

### 5.1 哈希算法

**算法描述**: 用于快速字符串匹配的哈希算法。

**代码实现**:
```python
B = 26
mod = 10**9+7
t = input()
s = input()
n, m = len(s), len(t)
ans = 0

# 计算前缀哈希
hash = [0]*(n+1)
for i in range(1, n+1):
    hash[i] = hash[i-1]*B + ord(s[i-1]) - ord("A")
    hash[i] %= mod

# 计算目标字符串哈希值
numT = 0
for c in t:
    numT = numT*B + ord(c) - ord("A")
    numT %= mod

# 匹配
p = (B**m) % mod
for l in range(1, len(s)+1):
    r = l + m - 1
    if r > n:
        break
    if (hash[r] - hash[l-1]*p%mod + mod) % mod == numT:
        ans += 1
print(ans)
```

**复杂度分析**: 时间复杂度O(n+m)，空间复杂度O(n)

### 5.2 进制转换

**算法描述**: 十进制与其他进制之间的转换。

**代码实现**:
```python
# 十进制转k进制
int_to_char = "0123456789ABCDEF"
def Ten_to_k(k, x):
    ans = ""
    while x != 0:
        ans += int_to_char[x % k]
        x //= k
    return ans[::-1]

# k进制转十进制
def k_to_10(k, x):
    ans = 0
    x = x[::-1]
    for i in range(len(x)):
        ans += int(x[i]) * (k**i)
    return ans

print(k_to_10(2, "010"))  # 示例：二进制010转十进制
```

**复杂度分析**: 时间复杂度O(log x)，空间复杂度O(log x)

**应用场景**: 数制转换、编码解码

### 5.3 二维前缀和差分

**算法描述**: 二维数组的前缀和与差分操作，用于快速计算矩形区域和。

**核心思想**:
- 前缀和：sum[i][j] = sum[i-1][j] + sum[i][j-1] - sum[i-1][j-1] + a[i][j]
- 区域和：area(x1,y1,x2,y2) = sum[x2][y2] - sum[x1-1][y2] - sum[x2][y1-1] + sum[x1-1][y1-1]

**代码实现**:
```python
# 二维前缀和模板
def build_prefix_sum_2d(matrix):
    n, m = len(matrix), len(matrix[0])
    prefix = [[0]*(m+1) for _ in range(n+1)]

    for i in range(1, n+1):
        for j in range(1, m+1):
            prefix[i][j] = (prefix[i-1][j] + prefix[i][j-1]
                          - prefix[i-1][j-1] + matrix[i-1][j-1])
    return prefix

def query_area_sum(prefix, x1, y1, x2, y2):
    return (prefix[x2][y2] - prefix[x1-1][y2]
            - prefix[x2][y1-1] + prefix[x1-1][y1-1])

# 二维差分模板
def build_diff_2d(n, m):
    return [[0]*(m+2) for _ in range(n+2)]

def add_area(diff, x1, y1, x2, y2, val):
    diff[x1][y1] += val
    diff[x1][y2+1] -= val
    diff[x2+1][y1] -= val
    diff[x2+1][y2+1] += val

def restore_from_diff(diff, n, m):
    result = [[0]*m for _ in range(n)]
    for i in range(1, n+1):
        for j in range(1, m+1):
            diff[i][j] += diff[i-1][j] + diff[i][j-1] - diff[i-1][j-1]
            result[i-1][j-1] = diff[i][j]
    return result
```

**复杂度分析**: 预处理O(n*m)，查询O(1)

**应用场景**: 矩形区域查询、二维区间修改

---

## 算法复杂度对比表

| 算法类别 | 算法名称 | 时间复杂度 | 空间复杂度 | 适用场景 |
|---------|---------|-----------|-----------|----------|
| 动态规划 | 0-1背包 | O(n*V) | O(V) | 选择问题 |
| 动态规划 | 完全背包 | O(n*V) | O(V) | 无限选择 |
| 动态规划 | 多重背包 | O(V*Σlog(si)) | O(V) | 有限选择 |
| 动态规划 | 最长公共子序列 | O(n*m) | O(n*m) | 序列匹配 |
| 动态规划 | 最长回文串 | O(n) | O(n) | 回文检测 |
| 树算法 | LCA | O(n log n) + O(log n) | O(n log n) | 树上查询 |
| 树算法 | 树上差分 | O(n log n) | O(n) | 路径修改 |
| 树算法 | 树的直径 | O(n) | O(n) | 最长路径 |
| 图算法 | 多源BFS | O(n*m) | O(n*m) | 最短距离 |
| 图算法 | 高级BFS | O(nm log(nm)) | O(n*m) | 带权最短路 |
| 数据结构 | 字典树 | O(L) | O(总长度) | 字符串查询 |
| 数据结构 | 0-1字典树 | O(32) | O(32*n) | 异或问题 |
| 数据结构 | 并查集 | O(α(n)) | O(n) | 集合操作 |
| 数据结构 | 单调栈 | O(n) | O(n) | 单调性问题 |
| 字符串 | KMP | O(n+m) | O(m) | 模式匹配 |
| 字符串 | 哈希 | O(n+m) | O(n) | 快速匹配 |

---

## 常见问题与解决方案

### 1. 动态规划问题
**Q**: 如何确定状态转移方程？
**A**:
1. 明确状态定义（dp数组含义）
2. 找到状态间的递推关系
3. 确定边界条件
4. 考虑优化空间复杂度

### 2. 树算法问题
**Q**: LCA算法为什么要用倍增？
**A**: 倍增可以将查询复杂度从O(n)优化到O(log n)，通过预处理每个节点向上走2^i步的祖先，实现快速跳跃。

### 3. 数据结构问题
**Q**: 什么时候使用字典树？
**A**:
- 需要存储大量字符串并快速查询前缀
- 字符串集合的前缀统计问题
- 自动补全功能

### 4. 图算法问题

**Q**: BFS和DFS的选择原则？
**A**:

- BFS：最短路径、层次遍历、状态空间搜索
- DFS：连通性检测、拓扑排序、回溯算法
