n = int(input())
G = [[] for _ in range(n+1)]
for _ in range(n-1):
    u,v,w = map(int,input().split())
    G[u].append([v,w])
    G[v].append([u,w])
def dfs(u,fa,d):
    global s
    if d[u]>d[s]:
        s = u
    for v,w in G[u]:
        if v==fa:
            continue
        d[v] = d[u]+w
        dfs(v,u,d)
d = [0]*(n+1)#d[i]表示节点i的深度
s = 1
dfs(1,0,d)
d[s]=0
d1 = [0]*(n+1)
dfs(s,0,d1)
ans = d1[s]
print(ans)
