#1.最本质
N,V = map(int,input().split())
dp = [[0]*(V+1) for i in range(N+1)]
for i in range(1,N+1):
    wi,vi = map(int,input().split())
    for j in range(1,V+1):
        if j<wi:
            dp[i][j] = dp[i-1][j]
        else:
            dp[i][j] = max(dp[i-1][j],dp[i][j-wi]+vi)#注意这里是i
print(dp[N][V])
#2.最优化
N,V = map(int,input().split())
dp = [0]*(V+1)
for i in range(1,N+1):
    wi,vi = map(int,input().split())
    for j in range(wi,V+1):#注意这里是正序
        dp[j] = max(dp[j],dp[j-wi]+vi)
print(dp[V])
#3.
N,V = map(int,input().split())
dp = [[0]*(V+1) for i in range(2)]
for i in range(1,N+1):
  wi,vi = map(int,input().split())
  for j in range(1,V+1):
    if j<wi:
      dp[i%2][j] = dp[(i-1)%2][j]
    else:
      dp[i%2][j] = max(dp[(i-1)%2][j],dp[i%2][j-wi]+vi)
print(dp[N%2][V])
