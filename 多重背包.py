#1.最本质
N,V = map(int,input().split())
dp = [[0]*(V+1) for i in range(N+1)]
for i in range(1,N+1):
    wi,vi,si = map(int,input().split())
    for j in range(1,V+1):
        for k in range(min(si,j//wi)+1):
            dp[i][j] = max(dp[i][j],dp[i-1][j-k*wi]+k*vi)
print(dp[N][V])
#2.最优化
N,V = map(int,input().split())
#转换0-1
w_v = []
for i in range(N):
    wi,vi,si = map(int,input().split())
    k = 1
    while si>=k:
        w_v.append((k*wi,k*vi))
        si-=k
        k*=2
    if si!=0:
        w_v.append((si*wi,si*vi))
#0-1背包
dp = [0]*(V+1)
for w,v in w_v:
    for j in range(V,w-1,-1):
        dp[j] = max(dp[j],dp[j-w]+v)
print(dp[V])
#3.
N,V = map(int,input().split())
dp = [[0]*(V+1) for i in range(2)]
for i in range(1,N+1):
    wi,vi,si = map(int,input().split())
    for j in range(1,V+1):
      for k in range(0,min(si,j//wi)+1):
        dp[i%2][j] = max(dp[i%2][j],dp[(i-1)%2][j-k*wi]+k*vi)
print(dp[N%2][V])
