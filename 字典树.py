class TreeNode():
    def __init__(self):
        self.nodes={}
        self.cnt=0
    def insert(self,s):
        #初始化游标位置
        curr = self
        for i in s:
            if i not in curr.nodes.keys():
                curr.nodes[i] = TreeNode()
            curr = curr.nodes[i]
            curr.cnt+=1
    def check(self,s):
        curr = self
        for i in s:
            if i not in curr.nodes.keys():
                return 0
            curr = curr.nodes[i]
        return curr.cnt
n,m = map(int,input().split())
root=TreeNode()
for i in range(n):
    s = input()
    root.insert(s)
for i in range(m):
    s = input()
    print(root.check(s))
        
