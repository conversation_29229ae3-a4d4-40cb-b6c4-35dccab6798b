from collections import deque
import sys
input = sys.stdin.readline
q = deque()
n,m,t = map(int,input().split())
#dis[i][j]表示暖气到达i,j所用的时间
dis = [[-1]*(m+1) for i in range(n+1)]
ans = 0
for i in range(t):
    x,y = map(int,input().split())
    q.append((x,y))
    dis[x][y]=0
while q:
    x,y = q.popleft()
    ans = max(ans,dis[x][y])
    for dx,dy in [(-1,0),(1,0),(0,-1),(0,1),(1,1),(-1,-1),(1,-1),(-1,1)]:
        nx,ny = x+dx,y+dy
        if 1<=nx<=n and 1<=ny<=m and dis[nx][ny]==-1:
            dis[nx][ny] = dis[x][y]+1
            q.append((nx,ny))
print(ans)
#更新答案的方式思维
