B = 26
mod = 10**9+7
t = input()
s = input()
n,m = len(s),len(t)
ans = 0
#hash前缀
hash = [0]*(n+1)#hash[i]表示s中前i个元素的哈希值
for i in range(1,n+1):#遍历s下标
  hash[i] = hash[i-1]*B+ord(s[i-1])-ord("A")
  hash[i] %= mod
#求t的哈希值
numT = 0
for c in t:
  numT = numT*B + ord(c)-ord("A")
  numT %= mod
#判断区间哈希值和t的哈希值是否相等，如果相等，答案加1
p = (B**m)%mod
for l in range(1,len(s)+1):#枚举左端点
  r = l+m-1#右端点
  if r>n:
    break
  if (hash[r]-hash[l-1]*p%mod+mod)%mod == numT:
    ans += 1
print(ans)
