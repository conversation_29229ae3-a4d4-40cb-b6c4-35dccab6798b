n = int(input())
s = input()
#Next[i]表示对于幸运字符串的前i的元素的最长公共前后缀的长度
Next = [0]*(n+1)
def get_next(t):
    for i in range(1,len(t)):
        j = Next[i]
        while j>0 and t[i]!=t[j]:
            j = Next[j]
        if t[i]==t[j]:
            Next[i+1]=j+1
        else:
            Next[i+1]=0
def KMP(s,t):
    get_next(t)
    j = 0
    ans = 0
    for i in range(len(s)):
        while j>0 and t[j]!=s[i]:
            j = Next[j]
        if t[j]==s[i]:
            j+=1
        if j==len(t):
            ans+=1
            j=Next[j]
    return ans
count = 0
for i in range(1,n+1):
    t = s[:i]
    count+=KMP(s,t)
print(count)
