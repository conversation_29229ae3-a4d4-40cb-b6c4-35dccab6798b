#单调栈
def right_bigger(a,n):
    ans = [-1]*(n+1)
    stack = []
    for i,x in enumerate(a):
        if i==0:
            continue
        while stack and a[stack[-1]]<x:
            ans[stack[-1]]=i
            stack.pop()
        stack.append(i)
    return ans[1:]

def left_bigger(a,n):
    ans = [-1]*(n+1)
    stack = []
    for i in range(n,0,-1):
        x = a[i]
        while stack and a[stack[-1]]<x:
            ans[stack[-1]] = i
            stack.pop()
        stack.append(i)
    return ans[1:]

N = int(input())
lst = [0]+list(map(int,input().split()))
print(*left_bigger(lst,N))
print(*right_bigger(lst,N))
#单调队列求连续k的长度区间最大值，直接暴力O(n)复杂度#单调队列求连续k的长度区间最大值，直接暴力O(n)复杂度
