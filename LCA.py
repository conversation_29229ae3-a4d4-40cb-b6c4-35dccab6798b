import sys
sys.setrecursionlimit(10000)
input = sys.stdin.readline
#定义dfs,作用:得到p数组与deep数组
def dfs(u,fa):
    #初始化
    deep[u] = deep[fa]+1
    p[u][0] = fa
    for i in range(1,21):
        p[u][i]=p[p[u][i-1]][i-1]
    for v in G[u]:
        if v==fa:
            continue
        dfs(v,u)
#定义lca函数
def lca(x,y):
    if deep[x]<deep[y]:
        #调整至x深度大，避免分类讨论，压缩代码量
        x,y = y,x
    for i in range(20,-1,-1):
        if deep[p[x][i]]>=deep[y]:
            x = p[x][i]
    '''此时x，y的深度相同'''
    if x==y:
        return x
    '''接下来一起向上走'''
    for i in range(20,-1,-1):
        if p[x][i]!=p[y][i]:
            x,y = p[x][i],p[y][i]
    '''for循环后，此时找到最近公共祖先，就是他们的父亲'''
    return p[x][0]
#1.数据输入
n = int(input())
G = [[] for i in range(n+1)]
for _ in range(n-1):
    u,r = map(int,input().split())
    G[u].append(r)
    G[r].append(u)
#2.预处理阶段
deep = [0]*(n+1)
p = [[0]*21 for i in range(n+1)]
dfs(1,0)
'''这里必须从根节点开始'''
#3.结果输出
q = int(input())
for _ in range(q):
    a,b = map(int,input().split())
    print(lca(a,b))
