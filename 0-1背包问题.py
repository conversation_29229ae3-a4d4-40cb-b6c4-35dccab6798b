#1.最本质
n,V = map(int,input().split())#n个物品，背包的容量为V
dp = [[0]*(V+1) for _ in range(n+1)]#dp[i][j]表示前i的物品，背包容量为j时，能装的最大价值
for i in range(1,n+1):
    w,v = map(int,input().split())
    for j in range(1,V+1):
        if j>=w:
            dp[i][j] = max(dp[i-1][j],dp[i-1][j-w]+v)
        else:
            dp[i][j] = dp[i-1][j]
print(dp[n][V])
#2.最优化
n,V = map(int,input().split())
dp = [0]*(V+1)
for i in range(1,n+1):
    w,v = map(int,input().split())
    for j in range(V,w-1,-1):
        dp[j] = max(dp[j],dp[j-w]+v)
print(dp[V])
#3.
n,V = map(int,input().split())#n个物品，背包的容量为V
dp = [[0]*(V+1) for _ in range(2)]#dp[i][j]表示前i的物品，背包容量为j时，能装的最大价值
for i in range(1,n+1):
    w,v = map(int,input().split())
    for j in range(1,V+1):
        if j>=w:
            dp[i%2][j] = max(dp[(i-1)%2][j],dp[(i-1)%2][j-w]+v)
        else:
            dp[i%2][j] = dp[(i-1)%2][j]
print(dp[n%2][V])
