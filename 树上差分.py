#1.数据输入阶段
import sys
sys.setrecursionlimit(100000)
input = sys.stdin.readline
n,k = map(int,input().split())#n个节点，k个操作
#2.处理邻接表
G = [[] for _ in range(n+1)]
for i in range(n-1):
    u,v = map(int,input().split())
    G[u].append(v)
    G[v].append(u)
#3.lca阶段
deep = [0]*(n+1)#depp[i]表示节点i的深度
p = [[0]*21 for i in range(n+1)]#p[i][j]表示节点i往上走2^j步到达的节点
def dfs(u,fa):
    deep[u] = deep[fa]+1
    p[u][0] = fa
    for i in range(1,21):
        p[u][i] = p[p[u][i-1]][i-1]
    for v in G[u]:
        if v == fa:
            continue
        dfs(v,u)
def lca(x,y):
    #保证x更深，这样减少了分类讨论，优化了代码
    if deep[x]<deep[y]:
        x,y = y,x
    for i in range(20,-1,-1):#注意这里从大到小
        if deep[p[x][i]]>=deep[y]:
            x = p[x][i]
    #此时deep[x] = deep[y]
    if x==y:
        return x
    for i in range(20,-1,-1):#注意这里从大到小
        if p[x][i]!=p[y][i]:#注意这里是!=
            x = p[x][i]
            y = p[y][i]
    return p[x][0]#还得向上走一步
dfs(1,0)#注意从树的根节点开始,一般是1,否则还需要查找根节点
#4.树上差分与答案输出阶段
c = [0]*(n+1)#c[i]表示节点i的权值，因为这是模板，所有初始化为0，具体题目需要把具体数组加上处理后的该数组，得到答案
for i in range(k):
    x,y = map(int,input().split())
    c[x]+=1
    c[y]+=1
    c[lca(x,y)]-=1
    c[p[lca(x,y)][0]]-=1
def dfs2(u,fa):
    for v in G[u]:
        if v==fa:
            continue
        dfs2(v,u)
        c[u]+=c[v]
dfs2(1,0)#注意从树的根节点开始,一般是1,否则还需要查找根节点(定义rudu列表,进行标记,rudu为0的即为根节点),否则可能对树的节点权值操作不完整
print(max(c))
