def extend(s,l,r):
    while l>=0 and r<len(s) and s[l]==s[r]:
        l-=1
        r+=1
    return (r-l-2)//2
def longestPalindroma(s):
    s = '#'+'#'.join(list(s))+'#'
    dp = [0]*(len(s))
    center,right = 0,0
    for i in range(1,len(s)):
        if i>right:
            dp[i] = extend(s,i,i)
        else:
            i_sym = center*2-i
            min_len = min(dp[i_sym],right-i)
            dp[i] = extend(s,i-min_len,i+min_len)
        if i+dp[i]>right:
            center,right = i,i+dp[i]
    return max(dp)
s = input()
print(longestPalindroma(s))
