n = int(input())
a = [0]+list(map(int,input().split()))
dp = [0]*(n+1)#dp[i]表示以a[i]结尾的最长上升子序列长度
for i in range(1,n+1):
    dp[i]=1#初始化
    for j in range(1,i):
        if a[j]<a[i]:
            dp[i] = max(dp[i],dp[j]+1)
dp1 = [0]*(n+1)#dp[i]表示以a[i]开头的最长下降子序列长度
for i in range(n,0,-1):
    dp1[i]=1#初始化
    for j in range(i+1,n+1):
        if a[j]<a[i]:
            dp1[i] = max(dp1[i],dp1[j]+1)
ans = max([dp[i]+dp1[i]-1 for i in range(1,n+1)])
print(n-ans)
